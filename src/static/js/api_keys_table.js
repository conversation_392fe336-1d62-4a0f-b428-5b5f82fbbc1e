const $table = $('#api-keys-table');
const $modal = $('#key-data')
let releasedIndexes = [];

const packageData = [
    { id: "employee", title: "Employee package (god mode)" },
    { id: "eap", title: "Network Asset Metrics (1d, REST API)" },
    { id: "ndp_rt", title: "Network Asset Metrics (1d, 1h, 1m, 1b, REST API, WebSocket API)" },
    { id: "realtime_asset_metrics", title: "Network Asset Metrics (1h, 1m, 1b, WebSocket API)" },
    { id: "market_metrics_package", title: "Market Data Metrics (all freq, REST API)" },
    { id: "market_data", title: "Market Data Feed Legacy (Spot+Derivatives, REST API)" },
    { id: "market_data_feed_package", title: "Market Data Feed (Spot+Derivatives, REST API, WebSocket API)" },
    { id: "market_data_feed_cme", title: "Market Data Feed (Spot+Derivatives, including CME, REST API, WebSocket API)" },
    { id: "market_data_feed_erisx", title: "Market Data Feed (Spot+Derivatives, including Erisx, REST API, WebSocket API)" },
    { id: "aggregated_spread_quotes", title: "Aggregated Spread / Asset & pair quotes (WebSocket API)" },
    { id: "defi_market_data", title: "DeFi Market Data (REST API)" },
    { id: "defi_spot_market_data", title: "DeFi Market Data (Spot, REST API, WebSocket API)" },
    { id: "flat_files_market_data", title: "Flat Files Market Data" },
    { id: "erisx_market_data", title: "ErisX/Cboe Digital Market Data" },
    { id: "dydx_market_data", title: "dYdX Market Data" },
    { id: "cme_market_data_delayed", title: "CME Data Delayed 1h (REST API)" },
    { id: "cme_market_data", title: "CME Data Realtime (REST API)" },
    { id: "reference_rates_ge_1h", title: "Reference Rates (1d, 1h, REST API)" },
    { id: "websocket_reference_rates_bundle", title: "Reference Rates (all freq, REST API, WebSocket API)" },
    { id: "principal_prices_ge_1h", title: "Principal Market Price (1d, 1h, REST API)" },
    { id: "websocket_principal_prices_bundle", title: "Principal Market Price (all freq, REST API, WebSocket API)" },
    { id: "chain_monitor", title: "FARUM - Everything (REST API)" },
    { id: "chain_monitor_asset_metrics", title: "FARUM - Asset Metrics (REST API)" },
    { id: "asset_chains", title: "BTC Fork Monitor / Asset chains (REST API)" },
    { id: "mining_pool_tips", title: "BTC Mining Pool Monitor / Mining pool tips summary (REST API)" },
    { id: "tx_tracker", title: "Transaction Tracker - All Assets (REST API)" },
    { id: "solana", title: "Network Asset Metrics - Solana only (1d, REST API)" },
    { id: "atlas_v2_released", title: "Atlas - All Assets (REST API)" },
    { id: "atlas_v2_labs", title: "Atlas - Experimental Assets (REST API)" },
    { id: "atlas_v2_internal", title: "Atlas - Internal Assets (REST API)" },
    { id: "blockchain_job_all_assets", title: "Atlas - All Assets (ASYNC API)" },
    { id: "blockchain_job_released_assets", title: "Atlas - Released Assets (ASYNC API)" },
    { id: "taxonomy_data", title: "Taxonomy/Datonomy (REST API)" },
    { id: "asset_profiles", title: "Asset & Network Profiles (REST API)" },
    { id: "tagging_released", title: "Address Tagging (REST API)" },
    { id: "tags", title: "tags (god mode, REST API)" },
    { id: "security_master", title: "Security Master (REST API)" },
    { id: "research", title: "Research Bundle (1d, REST API)" },
    { id: "defi_project_metrics", title: "Network Data: Balance Sheets (REST API)" },
    { id: "community", title: "community" },
    { id: "asset_metrics", title: "asset_metrics (god mode, REST API)" },
    { id: "indexes", title: "indexes (god mode, REST API, WebSocket API)" },
    { id: "fidelity_indexes", title: "Fidelity indexes (Levels and constituents)" },
    { id: "datonomy_indexes", title: "Datonomy Indexes (Levels and constituents)" },
    { id: "aark_indexes", title: "Aark Indexes (Levels and constituents)" }
];

function generatePackageCheckboxes() {
    const checkboxContainer = $('#packages');

    // Add new checkboxes
    packageData.forEach(pkg => {
        const checkboxHtml = `
            <div class="custom-control custom-checkbox">
                <input class="custom-control-input" type="checkbox" id="${pkg.id}">
                <label class="custom-control-label" for="${pkg.id}">
                    ${pkg.title}
                </label>
            </div>
        `;
        checkboxContainer.append(checkboxHtml);
    });
}

const indexTemplate = (name, active) => `<button type="button" class="btn btn-bd-primary me-1 mb-1 btn-sm ${active ? 'active' : ''}" data-bs-toggle="button" aria-pressed="true">${name}</button>`;

function renderIndexes(indexes) {
    let html = '';
    for (let i in releasedIndexes) {
        html += indexTemplate(releasedIndexes[i], indexes.includes(releasedIndexes[i]))
    }
    return "<div class='px-1 pt-1'>" + html + "</div>";
}

function removePrefix(str, prefix) {
    str = str.trim()
    if (str.startsWith(prefix)) {
        return str.slice(prefix.length);
    } else {
        return str;
    }
}

window.actionEvents = {
    'click .edit': function (e, value, row, index) {
        const script = row.script.map(item => removePrefix(item, "+ "))
        function hasPackage(packageName) {
            return script.some(item => item === packageName)
        }
        $modal.find('#submit-key')[0].dataset.apiKey = row.api_key
        $modal.find('#submit-key')[0].dataset.submissionType = 'PATCH'
        $modal.find('#submit-key')[0].dataset.submissionUrl = '/api/api_keys/' + row.api_key
        $modal.find('#submit-key').text('Save')
        $modal.find('input').val('').removeAttr('checked').removeAttr('selected')
        $modal.find('.modal-title').text(row.api_key)
        $modal.find('#revoked-badge').toggle(row.revoked)
        $modal.find('#company-name').val(row.company)
        $modal.find('#description').val(row.description)
        $modal.find('#poc-name').val(row.poc_name)
        $modal.find('#poc-email').val(row.poc_email)
        $modal.find('#expiration-date').val(row.expires)

        // Set checkbox values from package data
        packageData.forEach(pkg => {
            $modal.find(`#${pkg.id}`).prop("checked", hasPackage(pkg.id));
        });

        $modal.find('div#idx-levels').html(renderIndexes(row.indexes))
        $modal.find('#wrapper-for-create-another-key').hide()
        $modal.modal('show')
    },
    'click .revoke': function (e, value, row, index) {
        $.post(`/api/${row.api_key}/revoke`, function () {
            toastr.info(`Key '${row.api_key}' has been revoked.`, 'Server response')
            row.revoked = true
            e.currentTarget.closest('tr').classList.add('text-decoration-line-through')
        }).fail(makeErrorReporter('Could not revoke the key.'))
    }
}

function initTable() {
    $table.bootstrapTable('destroy').bootstrapTable({
        columns: [
            [{
                title: 'Key',
                field: 'api_key',
                halign: 'center',
                falign: 'left',
                valign: 'middle',
            }, {
                title: 'Company',
                field: 'company',
                align: 'center',
                valign: 'middle',
            }, {
                title: 'Description',
                field: 'description',
                halign: 'center',
                falign: 'left',
                valign: 'middle',
            }, {
                title: 'Expires',
                field: 'expires',
                align: 'center',
                valign: 'middle',
                sortable: true,
            }, {
                field: 'key_actions',
                title: 'Actions',
                align: 'center',
                valign: 'middle',
                class: 'align-middle',
                cellStyle: { css: { 'padding': '0', } },
                clickToSelect: false,
                events: window.actionEvents,
                formatter: actionsFormatter,
                searchable: false
            }]
        ]
    })
    $table.on('post-body.bs.table', function (e, data) {
        $('[data-bs-toggle="tooltip"]').tooltip()
    })
}

function submitApiKey(e) {
        let key = {
            company: $('#company-name').val(),
            description: $('#description').val(),
            poc_name: $('#poc-name').val(),
            poc_email: $('#poc-email').val(),
            expires: $('#expiration-date').val(),
            packages: {},
            revoked: false,
            indexes: getActiveButtons($('div#idx-levels')),
        }

        // Dynamically add all packages
        packageData.forEach(pkg => {
            key.packages[pkg.id] = $(`#${pkg.id}`).is(":checked");
        });

        let errorMessage = "Cannot submit a key, check input params (sometimes expiration format/date is the cause). If all good, please retry."
        let dataset = e.currentTarget.dataset

        $.ajax({
            type: dataset.submissionType,
            url: dataset.submissionUrl,
            dataType: 'json',
            data: JSON.stringify(key),
            contentType: 'application/json',
            success: function (data) {
                if (dataset.submissionType === 'PATCH') {
                    toastr.info(`Key for ${key.company} updated successfully <br/>${dataset.apiKey}.`, 'Server response')
                    $table.bootstrapTable('updateByUniqueId', {id: dataset.apiKey, row: {...data}})
                } else if (dataset.submissionType === 'POST') {
                    toastr.info(`Key for ${key.company} created successfully <br/>${data.api_key}.`, 'Server response')
                    $table.bootstrapTable('append', data)

                    // Update modal for the newly created key
                    $modal.find('#submit-key')[0].dataset.apiKey = data.api_key
                    $modal.find('#submit-key')[0].dataset.submissionType = 'PATCH'
                    $modal.find('#submit-key')[0].dataset.submissionUrl = '/api/api_keys/' + data.api_key
                    $modal.find('#submit-key').text('Save')
                    $modal.find('.modal-title').text(data.api_key)

                    copySummary()

                    // Check if "Create another" is checked
                    if ($modal.find('#create-another-key').prop('checked') === true) {
                        // Keep form values but reset modal state for creating a new key
                        $modal.find('#submit-key')[0].dataset.submissionType = 'POST'
                        $modal.find('#submit-key')[0].dataset.submissionUrl = '/api/create'
                        $modal.find('#submit-key').text('Create')
                        $modal.find('.modal-title').text('Create New Key')
                        // Note: We intentionally do NOT call resetFormValues() to preserve form content
                    } else {
                        // Standard workflow - form was already updated above to show "Save" button
                        // No additional action needed as the form is already in edit mode
                    }
                }
            },
            fail: makeErrorReporter(errorMessage)
        }).fail(makeErrorReporter(errorMessage));
    }

function resetFormValues() {
    $modal.find('form')[0].reset()
    $modal.find('input').val('')
    $modal.find('input[type=checkbox]').prop('checked', false)
    $modal.find('select').prop('selectedIndex', 0)
}
function resetForm() {
    resetFormValues()
    $modal.find('#submit-key')[0].dataset.submissionType = 'POST'
    $modal.find('#submit-key')[0].dataset.submissionUrl = '/api/create'
    $modal.find('#submit-key').text('Create')
    $modal.find('.modal-title').text('Create New Key')
    $modal.find('div#idx-levels').html(renderIndexes([]))
}
function copySummary() {
    // Get API key info
    const apiKey = $modal.find('#submit-key')[0].dataset.apiKey || 'NEW KEY';
    const company = $('#company-name').val();
    const description = $('#description').val();
    const pocName = $('#poc-name').val();
    const pocEmail = $('#poc-email').val();
    const expiration = $('#expiration-date').val() || 'N/A';

    // Get selected packages
    let selectedPackages = [];
    packageData.forEach(pkg => {
        if ($(`#${pkg.id}`).is(":checked")) {
            selectedPackages.push(pkg.title);
        }
    });

    // Get selected indexes
    const selectedIndexes = getActiveButtons($('div#idx-levels'));

    // Format the summary
    let summary = `${apiKey}\n`;
    summary += `Company: ${company}\n`;
    summary += `Description: ${description}\n`;
    summary += `Name & Email: ${pocName} - ${pocEmail}\n`;
    summary += `Expiration: ${expiration}\n\n`;

    summary += `PACKAGES:\n`;
    if (selectedPackages.length > 0) {
        selectedPackages.forEach(pkg => {
            summary += `- ${pkg}\n`;
        });
    } else {
        summary += `N/A\n`;
    }

    summary += `\nINDEXES:\n`;
    if (selectedIndexes.length > 0) {
        selectedIndexes.forEach(idx => {
            summary += `- ${idx}\n`;
        });
    } else {
        summary += `N/A\n`;
    }

    // Copy to clipboard
    navigator.clipboard.writeText(summary)
        .then(() => {
            toastr.success('API key summary copied to clipboard', 'Success');
        })
        .catch(err => {
            toastr.error('Could not copy summary to clipboard', 'Error');
            console.error('Error copying to clipboard: ', err);
        });
}
function initActionHandlers() {
    $('#create-new-key').click(function () {
        resetForm()
        $modal.find('#wrapper-for-create-another-key').show()
        $modal.find('#revoked-badge').hide()
        $modal.modal('show')
    })

    $modal.on('hidden.bs.modal', function () {
        resetFormValues()
    })

    $('#submit-key').click(submitApiKey)
    $('#copy-summary').click(copySummary);
}

function actionsFormatter(value, row, index) {
    return [
        '<div class="btn-toolbar gap-2" role="toolbar" style="justify-content: center">',
        '  <a class="edit" href="javascript:void(0)" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Edit">',
        '    <i class="bi bi-pencil-square" style="font-size: 1.5rem; color: #17a2b8;"></i>',
        '  </a>',
        '  <a class="revoke" href="javascript:void(0)" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Revoke">',
        '    <i class="bi bi-dash-circle" style="font-size: 1.5rem; color: #dc3545;"></i>',
        '  </a>',
        '</div>',
    ].join('')
}

function rowStyle(row, index) {
    let classes = []
    if (row.revoked) classes.push('text-decoration-line-through')
    return {
        classes: classes
    }
}

function detailFormatter(index, row) {
    return [
        '<div class="row">',
        '  <div class="col-auto">',
        '    <div class="form-floating">',
        '      <input type="text" readonly class="form-control-plaintext" id="detail-poc-name" value="' + row.poc_name + '">',
        '      <label for="detail-poc-name">Point of contact name</label>',
        '    </div>',
        '  </div>',
        '  <div class="col-auto">',
        '    <div class="form-floating">',
        '      <input type="email" readonly class="form-control-plaintext" id="detail-poc-email" value="' + row.poc_email + '">',
        '      <label for="detail-poc-email">Point of contact email</label>',
        '    </div>',
        '  </div>',
        '</div>',
    ].join('')
}

function getActiveButtons(div) {
    let activeButtons = div.find('button.active')
    return activeButtons.map(i => activeButtons[i].innerText).toArray()
}

function prefetchIndexes() {
    $.getJSON('/api/indexes', function (data) {
        releasedIndexes = data
    }).fail(function (jqXHR) {
        console.error("error:", jqXHR)
        let message = jqXHR.statusText ? jqXHR.statusText : "Unknown reason"
        toastr.warning(message, "Failed to get indexes.")
    });
}

function initToastr() {
    toastr.options = {
        closeButton: true,
        progressBar: true,
        timeOut: 0,
        extendedTimeOut: 0,
        tapToDismiss: false,
        showMethod: "show",
        hideMethod: "hide"
    };
}

$(function () {
    initToastr();
    initTable();
    generatePackageCheckboxes();
    initActionHandlers();
    prefetchIndexes();
})
